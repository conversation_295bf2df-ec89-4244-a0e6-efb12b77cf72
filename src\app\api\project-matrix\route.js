/*
 * =================================================================
 * FILE: src/app/api/project-matrix/route.js (CORRECTED)
 * =================================================================
 * This API is now corrected to get cookies directly from the request
 * object, resolving the server/client module conflict.
 */

import { NextResponse } from "next/server";
// REMOVED: import { cookies } from 'next/headers';
import jwt from "jsonwebtoken";
import prisma from "@/lib/prisma";

const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-key-that-is-long";
const COOKIE_NAME = "authToken";
const UNDEFINED_ID = "undefined";

export async function GET(request) {
  try {
    // --- Authentication (Corrected Method) ---
    // Get the cookie directly from the incoming request object
    const token = request.cookies.get(COOKIE_NAME);

    if (!token) {
      return NextResponse.json({ message: "Authentication required" }, { status: 401 });
    }

    const { userId } = jwt.verify(token.value, JWT_SECRET);
    if (!userId) {
      return NextResponse.json({ message: "Invalid token payload" }, { status: 401 });
    }

    // --- Get View Config from Query Params ---
    const { searchParams } = new URL(request.url);
    const viewConfig = {
      rows: searchParams.get("rows") || "lifeAspect",
      subRows: searchParams.get("subRows") || "project",
      columns: searchParams.get("columns") || "priority",
    };

    // --- Data Fetching ---
    const [lifeAspects, projects, outcomes, customFields, preferences] = await Promise.all([
      prisma.workitems_life_aspect.findMany({ where: { user_id: userId }, orderBy: { sort_order: "asc" } }),
      prisma.workitems_project.findMany({ where: { user_id: userId }, orderBy: { sort_order: "asc" } }),
      prisma.workitems_outcome.findMany({
        where: { user_id: userId },
        include: { project: true, custom_field_values: { include: { selected_options: true, field_definition: true } } },
      }),
      prisma.workitems_custom_field_definition.findMany({
        where: { user_id: userId },
        include: { choice_options: { orderBy: { sort_order: "asc" } } },
      }),
      prisma.auth_user_preferences.findUnique({ where: { user_id: userId } }),
    ]);

    // --- Dynamic Data Transformation ---
    const getDimensionData = (dimensionKey) => {
      switch (dimensionKey) {
        case "lifeAspect":
          return [{ id: UNDEFINED_ID, name: "? Undefined" }, ...lifeAspects];
        case "project":
          return [{ id: UNDEFINED_ID, name: "? Undefined" }, ...projects];
        default:
          const field = customFields.find((f) => f.id === dimensionKey || f.name.toLowerCase() === dimensionKey);
          return field ? [{ id: UNDEFINED_ID, value: "Undefined" }, ...field.choice_options] : [{ id: UNDEFINED_ID, value: "Undefined" }];
      }
    };

    const rowItems = getDimensionData(viewConfig.rows);
    const subRowItems = getDimensionData(viewConfig.subRows);
    const columnItems = getDimensionData(viewConfig.columns);

    const grid = {};

    const getOutcomeDimensionId = (outcome, dimensionKey) => {
      switch (dimensionKey) {
        case "lifeAspect":
          return outcome.project?.life_aspect_id || UNDEFINED_ID;
        case "project":
          return outcome.project_id || UNDEFINED_ID;
        default:
          const value = outcome.custom_field_values?.find(
            (v) => v.field_definition_id === dimensionKey || v.field_definition.name.toLowerCase() === dimensionKey
          );
          return value?.selected_options[0]?.id || UNDEFINED_ID;
      }
    };

    rowItems.forEach((row) => {
      grid[row.id] = {};
      subRowItems.forEach((subRow) => {
        grid[row.id][subRow.id] = {};
        columnItems.forEach((col) => {
          grid[row.id][subRow.id][col.id] = [];
        });
      });
    });

    outcomes.forEach((outcome) => {
      const rowId = getOutcomeDimensionId(outcome, viewConfig.rows);
      const subRowId = getOutcomeDimensionId(outcome, viewConfig.subRows);
      const colId = getOutcomeDimensionId(outcome, viewConfig.columns);

      if (grid[rowId] && grid[rowId][subRowId] && grid[rowId][subRowId][colId]) {
        grid[rowId][subRowId][colId].push({ id: outcome.id, name: outcome.name });
      }
    });

    // --- Final Assembly ---
    const responsePayload = {
      view: viewConfig,
      rows: rowItems.map((i) => ({ id: i.id, name: i.name || i.value })),
      subRows: subRowItems.map((i) => ({ id: i.id, name: i.name || i.value })),
      columns: columnItems.map((i) => ({ id: i.id, name: i.name || i.value })),
      grid,
      allOutcomes: outcomes.map((o) => ({ id: o.id, name: o.name })),
      topOutcomes: preferences?.preferences?.topOutcomes || [],
      customFields: customFields.map((cf) => ({ id: cf.id, name: cf.name })),
    };

    return NextResponse.json(responsePayload);
  } catch (error) {
    console.error("Dynamic Matrix API Error:", error);
    return NextResponse.json({ message: "An internal server error occurred" }, { status: 500 });
  }
}
